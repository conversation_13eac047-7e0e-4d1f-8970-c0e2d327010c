@tailwind components;
@tailwind utilities;

@layer components {
  .all-\[unset\] {
    all: unset;
  }
}

:root {
  --body-heavy-font-family: "Microsoft Tai Le", Helvetica;
  --body-heavy-font-size: 32px;
  --body-heavy-font-style: normal;
  --body-heavy-font-weight: 700;
  --body-heavy-letter-spacing: 0px;
  --body-heavy-line-height: 48px;
  --body-small-font-family: "Microsoft Tai Le", Helvetica;
  --body-small-font-size: 24px;
  --body-small-font-style: normal;
  --body-small-font-weight: 400;
  --body-small-heavy-font-family: "Microsoft Tai Le", Helvetica;
  --body-small-heavy-font-size: 24px;
  --body-small-heavy-font-style: normal;
  --body-small-heavy-font-weight: 700;
  --body-small-heavy-letter-spacing: 0px;
  --body-small-heavy-line-height: 36px;
  --body-small-letter-spacing: 0px;
  --body-small-line-height: 36px;
  --buttons-and-input-16-font-family: "Microsoft Tai Le", Helvetica;
  --buttons-and-input-16-font-size: 18px;
  --buttons-and-input-16-font-style: normal;
  --buttons-and-input-16-font-weight: 700;
  --buttons-and-input-16-letter-spacing: 0px;
  --buttons-and-input-16-line-height: 28px;
  --buttons-and-input-24-b-font-family: "Microsoft Tai Le", Helvetica;
  --buttons-and-input-24-b-font-size: 20px;
  --buttons-and-input-24-b-font-style: normal;
  --buttons-and-input-24-b-font-weight: 700;
  --buttons-and-input-24-b-letter-spacing: 0px;
  --buttons-and-input-24-b-line-height: 32px;
  --h1-font-family: "Thunderhouse Pro";
  --h1-font-size: 200px;
  --h1-font-style: normal;
  --h1-font-weight: 400;
  --h1-letter-spacing: 0px;
  --h1-line-height: 180px;
  --h2-font-family: "Thunderhouse Pro";
  --h2-font-size: 128px;
  --h2-font-style: normal;
  --h2-font-weight: 400;
  --h2-letter-spacing: 0px;
  --h2-line-height: 120px;
  --h3-font-family: "Thunderhouse Pro";
  --h3-font-size: 96px;
  --h3-font-style: normal;
  --h3-font-weight: 400;
  --h3-letter-spacing: 0px;
  --h3-line-height: 104px;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47.4% 11.2%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;

    --card: transparent;
    --card-foreground: 222.2 47.4% 11.2%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 100% 50%;
    --destructive-foreground: 210 40% 98%;

    --ring: 215 20.2% 65.1%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 224 71% 4%;
    --foreground: 213 31% 91%;

    --muted: 223 47% 11%;
    --muted-foreground: 215.4 16.3% 56.9%;

    --accent: 216 34% 17%;
    --accent-foreground: 210 40% 98%;

    --popover: 224 71% 4%;
    --popover-foreground: 215 20.2% 65.1%;

    --border: 216 34% 17%;
    --input: 216 34% 17%;

    --card: transparent;
    --card-foreground: 213 31% 91%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 1.2%;

    --secondary: 222.2 47.4% 11.2%;
    --secondary-foreground: 210 40% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;

    --ring: 216 34% 17%;

    --radius: 0.5rem;
    }

    @keyframes gentle-sway-x {
      0%, 100% {
        transform: translateX(0px);
      }
      50% {
        transform: translateX(10px);
      }
    }

    @keyframes gentle-sway-y {
      0%, 100% {
        transform: translateY(0px);
      }
      50% {
        transform: translateY(10px);
      }
    }

    .gentle-sway-y {
      animation: gentle-sway-y 3s ease-in-out infinite;
    }

    .gentle-sway-x {
      animation: gentle-sway-x 3s ease-in-out infinite;
    }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    scroll-behavior: smooth;
    background-color: #111111;
  }

  @font-face{
    font-family: "Thunderhouse Pro";
    src: url("https://db.onlinewebfonts.com/t/e8fafd4bab1ff229bb62067775369b8d.eot");
    src: url("https://db.onlinewebfonts.com/t/e8fafd4bab1ff229bb62067775369b8d.eot?#iefix")format("embedded-opentype"),
        url("https://db.onlinewebfonts.com/t/e8fafd4bab1ff229bb62067775369b8d.woff")format("woff"),
        url("https://db.onlinewebfonts.com/t/e8fafd4bab1ff229bb62067775369b8d.woff2")format("woff2"),
        url("https://db.onlinewebfonts.com/t/e8fafd4bab1ff229bb62067775369b8d.ttf")format("truetype"),
        url("https://db.onlinewebfonts.com/t/e8fafd4bab1ff229bb62067775369b8d.svg#Thunderhouse Pro")format("svg");
    font-weight:normal;
    font-style:normal;
    font-display:swap;
  }

  @font-face{
    font-family: "Microsoft Tai Le Bold";
    src: url("https://db.onlinewebfonts.com/t/22181869727dc4b201302cc1c78f9ace.eot");
    src: url("https://db.onlinewebfonts.com/t/22181869727dc4b201302cc1c78f9ace.eot?#iefix")format("embedded-opentype"),
        url("https://db.onlinewebfonts.com/t/22181869727dc4b201302cc1c78f9ace.woff")format("woff"),
        url("https://db.onlinewebfonts.com/t/22181869727dc4b201302cc1c78f9ace.woff2")format("woff2"),
        url("https://db.onlinewebfonts.com/t/22181869727dc4b201302cc1c78f9ace.ttf")format("truetype"),
        url("https://db.onlinewebfonts.com/t/22181869727dc4b201302cc1c78f9ace.svg#Microsoft Tai Le Bold")format("svg");
    font-weight:normal;
    font-style:normal;
    font-display:swap;
  }
}
