import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ElementLandingPage } from '../screens/ElementLandingPage/ElementLandingPage';
import { ElementContact } from '../screens/ElementContact/ElementContact';

const AppRoutes: React.FC = () => {
    return (
        <Router>
            <Routes>
                <Route path="/" element={<ElementLandingPage />} />
                <Route path="/talk-to-us" element={<ElementContact />} />
                <Route path="*" element={<ElementLandingPage />} />
            </Routes>
        </Router>
    );
};

export default AppRoutes;