import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ElementLandingPage } from "./screens/ElementLandingPage/ElementLandingPage";
import { ElementContact } from "./screens/ElementContact";

createRoot(document.getElementById("app")!).render(
  <StrictMode>
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<ElementLandingPage />} />
        <Route path="/talk-to-us" element={<ElementContact />} />
      </Routes>
    </BrowserRouter>
  </StrictMode>
);
