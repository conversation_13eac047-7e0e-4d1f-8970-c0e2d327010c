import React from "react";
import { <PERSON><PERSON> } from "../../components/ui/button";
import { Card, CardContent } from "../../components/ui/card";
import { Input } from "../../components/ui/input";
import { Label } from "../../components/ui/label";
import { Textarea } from "../../components/ui/textarea";
import { RiInstagramFill, RiTwitterXFill, RiWhatsappFill } from "react-icons/ri";
import { FaFacebook } from "react-icons/fa";
import { HeaderByAnima } from "../ElementLandingPage/sections/HeaderByAnima";

const socialMediaLinks = [
  { icon: "/img/facebook.png", handle: "@easymidia", link: "https://www.facebook.com/easymidia" },
  { icon: "/img/instagram.png", handle: "@easymidia", link: "https://www.instagram.com/agenciaeasymidia/" },
  { icon: "/img/x.png", handle: "@easymidia", link: "https://x.com/easymidia" },
];

export const ElementContact = (): JSX.Element => {
  React.useEffect(() => {
    window.scrollTo(0, 0);
  }, []);
  return (
    <div className="flex flex-row justify-center w-full">
      <div className="bg-[#0c0c0c] overflow-hidden w-[100%] h-[2500px] sm:h-[2500px] md:h-[1602px] relative bg-[url(/img/bg_section_8.png)] bg-blend-overlay">
        <div className="absolute w-full h-[1765px] flex justify-center bg-cover bg-[50%_50%]">
          <div className="flex flex-col w-[1100px] items-start relative">
            <section className="px-4 py-4 pt-[120px] self-stretch w-full flex-[0_0_auto] flex flex-col items-center justify-center relative" id="form">
              <div className="w-full flex justify-center bg-[#111111] h-[max-content] fixed top-0 left-0 z-10">
                <HeaderByAnima />
              </div>
              <div className="flex items-center flex-col sm:flex-col md:flex-row md:gap-2 lg:gap-8 relative self-stretch w-full flex-[0_0_auto]">
                <div className="w-[400px] sm:w-[640px] gap-6 flex flex-col items-center justify-center relative md:left-24 lg:left-0">
                  <img
                    className="absolute lg:w-[726px] md:w-[400px] bottom-[98px] sm:bottom-[100px] sm:-left-10 md:bottom-0 lg:h-52 md:top-18 md:-left-[10px] lg:top-[205px] xl:top-[175px] lg:-left-[20px] md:top-20 md:bg-contain"
                    alt="Pincelada roxa"
                    src="/img/pincelada-roxa-5.png"
                  />

                  <h1 className="relative self-stretch mt-[-1.00px] leading-[135px] sm:leading-[135px] -mt-[40px] sm:-mt-[40px] font-h3 font-[number:var(--h1-font-weight)] md:-top-16 lg:top-0 text-white text-[155px] sm:text-[185px] md:text-[155px] md:leading-[115px] lg:text-[length:var(--h1-font-size)] tracking-[var(--h1-letter-spacing)] leading-[var(--h1-line-height)] sm:leading-[var(--h1-line-height)] lg:leading-[var(--h1-line-height)] [font-style:var(--h1-font-style)]">
                    Fale conosco
                  </h1>

                  <p className="relative self-stretch font-body py-6 sm:px-0 sm:pt-14 font-[number:var(--body-font-weight)] lg:pt-20 text-white text-[20px] px-4 md:px-0 lg:px-0 sm:text-[24px] text-[length:var(--body-font-size)] lg:text-[26px] tracking-[var(--body-letter-spacing)] leading-[var(--body-line-height)] [font-style:var(--body-font-style)]">
                    Entre em contato e leve sua marca <br />
                    para o próximo nível!
                  </p>
                </div>

                <Card className="w-[400px] sm:w-[500px] md:w-[450px] pt-6 sm:pt-0 md:pt-0 lg:pt-0 lg:w-[640px] bg-transparent border-0 md:mr-28 lg:mr-0">
                  <CardContent className="p-0">
                    <form className="flex flex-col items-start gap-8">
                      <div className="flex flex-col items-start gap-6 relative self-stretch w-full">
                        <div className="flex flex-col items-start gap-1.5 relative self-stretch w-full">
                          <Label
                            htmlFor="name"
                            className="font-buttons-and-input-14 font-[number:var(--buttons-and-input-14-font-weight)] text-[#bcbfc5] text-[length:var(--buttons-and-input-14-font-size)] tracking-[var(--buttons-and-input-14-letter-spacing)] leading-[var(--buttons-and-input-14-line-height)] [font-style:var(--buttons-and-input-14-font-style)]"
                          >
                            Nome
                          </Label>
                          <Input
                            id="name"
                            placeholder="Nome completo"
                            className="pl-3 pr-14 py-4 bg-[#111111] rounded-sm border border-solid border-white font-buttons-and-input-16 font-[number:var(--buttons-and-input-16-font-weight)] text-white text-[length:var(--buttons-and-input-16-font-size)] tracking-[var(--buttons-and-input-16-letter-spacing)] leading-[var(--buttons-and-input-16-line-height)] [font-style:var(--buttons-and-input-16-font-style)]"
                          />
                        </div>

                        <div className="flex flex-col items-start gap-1.5 relative self-stretch w-full">
                          <Label
                            htmlFor="email"
                            className="font-buttons-and-input-14 font-[number:var(--buttons-and-input-14-font-weight)] text-[#bcbfc5] text-[length:var(--buttons-and-input-14-font-size)] tracking-[var(--buttons-and-input-14-letter-spacing)] leading-[var(--buttons-and-input-14-line-height)] [font-style:var(--buttons-and-input-14-font-style)]"
                          >
                            E-mail
                          </Label>
                          <Input
                            id="email"
                            placeholder="<EMAIL>"
                            className="pl-3 pr-14 py-4 bg-[#111111] rounded-sm border border-solid border-white font-buttons-and-input-16 font-[number:var(--buttons-and-input-16-font-weight)] text-white text-[length:var(--buttons-and-input-16-font-size)] tracking-[var(--buttons-and-input-16-letter-spacing)] leading-[var(--buttons-and-input-16-line-height)] [font-style:var(--buttons-and-input-16-font-style)]"
                          />
                        </div>

                        <div className="flex flex-col items-start gap-3 relative self-stretch w-full">
                          <Label
                            htmlFor="message"
                            className="font-buttons-and-input-16 font-[number:var(--buttons-and-input-16-font-weight)] text-[#bcbfc5] text-[length:var(--buttons-and-input-16-font-size)] tracking-[var(--buttons-and-input-16-letter-spacing)] leading-[var(--buttons-and-input-16-line-height)] [font-style:var(--buttons-and-input-16-font-style)]"
                          >
                            Mensagem
                          </Label>
                          <div className="relative w-full">
                            <Textarea
                              id="message"
                              placeholder="Escreva sua mensagem aqui..."
                              className="h-32 p-4 bg-[#111111] rounded-sm border border-solid border-white font-buttons-and-input-16 font-[number:var(--buttons-and-input-16-font-weight)] text-white text-[length:var(--buttons-and-input-16-font-size)] tracking-[var(--buttons-and-input-16-letter-spacing)] leading-[var(--buttons-and-input-16-line-height)] [font-style:var(--buttons-and-input-16-font-style)]"
                            />
                            <img
                              className="absolute w-1 h-[7px] bottom-4 right-4"
                              alt="Group"
                              src="/group-1.png"
                            />
                          </div>
                        </div>
                      </div>

                      <Button className="w-full relative overflow-hidden px-5 py-6 bg-[#fc0081] transition duration-700 ease-in-out border-4 border-solid border-white font-buttons-and-input-24-b font-[number:var(--buttons-and-input-24-b-font-weight)] text-white text-sm tracking-[var(--buttons-and-input-24-b-letter-spacing)] leading-[var(--buttons-and-input-24-b-line-height)] [font-style:var(--buttons-and-input-24-b-font-style)] hover:bg-[#d6006e] hover:scale-105 hover:shadow-lg group">
                        <div className="absolute inset-0 bg-gradient-to-r from-[#49036F] to-[#75003c] translate-x-[-100%] group-hover:translate-x-[0%] transition-transform duration-700 ease-in-out"></div>
                        <span className="relative z-10 text-md">ENVIAR MENSAGEM</span>
                      </Button>
                    </form>
                  </CardContent>
                </Card>
              </div>
            </section>

            <section className="flex items-center flex-col gap-6 relative self-stretch w-full flex-[0_0_auto]">
              <div className="flex flex-col sm:flex-col md:flex-row gap-0 w-full">
                <div className="relative w-[759px] h-[764px]">
                  <div className="relative h-[764px]">
                    <div className="absolute w-[734px] h-[764px] top-0 left-0 bg-[#fc0081] rounded-[367px/382px] blur-[92px] opacity-15" />
                    <img
                      className="absolute w-[720px] h-[560px] sm:w-[730px] mt-36 md:mt-0 lg:mt-0 sm:top-0 md:top-0 sm:h-[650px] md:w-[742px] md:h-[761px] top-[3px] left-[17px] object-contain"
                      alt="Img"
                      src="/img/img_section_contact.png"
                    />
                  </div>
                </div>

                <div className="flex flex-col w-full sm:w-full md:w-[640px] items-start justify-center relative md:-left-16 lg:left-0">
                  <div className="flex flex-col items-center gap-24 sm:gap-24 md:gap-14 lg:gap-20 relative self-stretch w-full flex-[0_0_auto] z-[1] top-[35px] mb-[150px]">
                    <h2 className="relative self-stretch mt-[-1.00px] text-center sm:text-center font-h2 font-[number:var(--h2-font-weight)] text-white text-[80px] sm:text-[100px] md:text-[90px] lg:text-[109px] md:text-left lg:text-center tracking-[var(--h2-letter-spacing)] leading-[90px] [font-style:var(--h2-font-style)]">
                      Siga a gente nas<br/> redes sociais
                    </h2>

                    <div className="inline-flex items-start flex-col sm:flex-col md:flex-row md:-left-12 lg:left-0 gap-6 relative flex-[0_0_auto]">
                      {socialMediaLinks.map((social, index) => (
                        <a
                          href={social.link}
                          key={index}
                          className="inline-flex items-center gap-3 sm:gap-3 md:gap-1 relative flex-[0_0_auto]"
                        >
                          <img
                            className="relative w-9 h-9 sm:w-9 sm:h-9 md:w-7 md:h-7 object-contain"
                            alt={`Social media icon ${index + 1}`}
                            src={social.icon}
                          />
                          <span className="relative w-fit mt-[-1.00px] font-[number:var(--body-small-font-weight)] text-[26px] sm:text-[26px] text-white md:text-[16px] lg:text-[22px] text-center tracking-[var(--body-small-letter-spacing)] leading-[var(--body-small-line-height)] whitespace-nowrap [font-style:var(--body-small-font-style)]">
                            {social.handle}
                          </span>
                        </a>
                      ))}
                    </div>
                  </div>

                  <img
                    className="absolute md:w-[415px] h-[115px] top-[140px] sm:top-[140px] left-10 sm:left-10 md:top-[295px] lg:w-[618px] h-[133px] lg:top-[300px] object-contain left-[-3px] z-0"
                    alt="Pincelada rosa"
                    src="/img/pincelada-rosa.png"
                  />
                </div>
              </div>
            </section>
          </div>
        </div>
        <div className="w-full absolute flex justify-center bottom-0 left-0 bg-[#111111]">
          <footer className="w-full sm:w-full md:w-[100%] px-4 sm:px-4 md:px-0 lg:w-[1100px] h-[100%] md:h-[115px] py-4 ">
            <div className="flex flex-col items-center w-full sm:flex-col md:flex-col lg:flex-row justify-between">
              <div className="flex items-center gap-2 w-full sm:w-full justify-between sm:justify-between md:w-auto">
                <img
                  className="w-[115px] h-[82px] object-cover"
                  alt="Easymidia"
                  src="/img/logo.png"
                />
    
                <div className="flex items-center gap-2">
                  <a href="">
                    <RiWhatsappFill className="w-8 h-8 text-[#00DC7C]" />
                  </a>
    
                  <a href="">
                    <FaFacebook className="w-7 h-7 text-[#00DC7C]" />
                  </a>
                  <a href="https://www.instagram.com/agenciaeasymidia/">
                    <RiInstagramFill className="w-8 h-8 text-[#00DC7C]" />
                  </a>
                  <a href="">
                    <RiTwitterXFill className="w-8 h-8 text-[#00DC7C]" />
                  </a>
                </div>
              </div>
    
              <div className="font-body-small font-[number:var(--body-small-font-weight)] text-[14px] sm:text-[18px] text-white md:text-[length:var(--body-small-font-size)] text-center tracking-[var(--body-small-letter-spacing)] leading-[var(--body-small-line-height)] md:whitespace-nowrap whitespace-wrap px-4 [font-style:var(--body-small-font-style)]">
                © 2025 Agência Easy Mídia. Todos os direitos reservados.
              </div>
            </div>
          </footer>
        </div>
      </div>
    </div>
  );
};