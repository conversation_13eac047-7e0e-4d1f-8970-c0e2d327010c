import { ChevronLeftIcon, ChevronRightIcon } from "lucide-react";
import React from "react";
import { Button } from "../../../../components/ui/button";
import { Card, CardContent, CardFooter } from "../../../../components/ui/card";
import { useState } from "react";

export const ProjectsByAnima = (): JSX.Element => {
  // Testimonial data for mapping
  const testimonials = [
    {
      id: 1,
      name: "Lucas Oioli",
      company: "CERUA",
      image: "/img/client_1.png",
      text: "Ocorreram diversos trabalhos, todos entregues com extrema qualidade e capricho pela equipe! O diferencial da empresa é o destaque criativo e a habilidade de transformar as ideias dos clientes em realidade, devido a capacidade de comunicação do grupo, que são super atenciosos e rápidos.\nRecomendo a EasyMidia para todos os tipos de trabalhos, sejam eles pequenos eventos universitário até grandes eventos.",
    },
    {
      id: 2,
      name: "<PERSON>ai<PERSON>",
      company: "Arapuca Eventos",
      image: "/img/client_2.png",
      text: "Trabalhar com a Easymidia foi uma experiência muito positiva. O trabalho é extremamente caprichoso, com atenção aos detalhes e sempre entregue dentro dos prazos estabelecidos. A equipe demonstra profissionalismo, criatividade e está sempre disposta a encontrar as melhores soluções para nossas demandas. Ficamos muito satisfeitos com os resultados e recomendamos a Easymidia para quem busca qualidade, compromisso e excelência.",
    },
    {
      id: 3,
      name: "Carol",
      company: "Poppins, Doutorada AQA",
      image: "/img/client_3.png",
      text: "As artes da EasyMídia deram um super destaque pro nosso evento! O design ficou incrível, do jeito que a gente queria, e ajudou muito na divulgação. Trabalho caprichado e de muita qualidade. Valeu demais!",
    },
    {
      id: 4,
      name: "Pédi",
      company: "AUG, AQA",
      image: "/img/client_4.png",
      text: "Trabalhar com o Gustavo, da Easymidia, é uma tranquilidade! Ele não tem tempo ruim: aceita demanda extra, vira noite se precisar e encara qualquer desafio com criatividade e bom humor. Se a arte não ficou do jeitinho que você imaginou, sem problema — ele refaz quantas vezes for preciso, sempre buscando a melhor versão. E o mais impressionante: o Gustavo tem uma habilidade quase mágica de entender o que você pensou (mesmo quando você nem sabe explicar direito) e traduzir isso em uma arte incrível, cheia de referências maneiras e com a sua cara. Se você quer alguém que realmente veste a camisa do projeto, é ele!",
    },
    {
      id: 5,
      name: "Alberto Cardoso",
      company: "Bendito Grau Eventos",
      image: "/img/client_5.png",
      text: "A experiência de trabalhar com a Easymidia foi extremamente gratificante. A equipe se mostrou altamente comprometida, sempre entregando resultados com grande qualidade. A criatividade nas soluções apresentadas superaram nossas expectativas. Fiquei muito satisfeito com o trabalho desenvolvido.",
    },
  ];

  // Pagination indicators
  const paginationCount = 5;
  const activePage = 0;

  // State for managing the current testimonial
  const [currentIndex, setCurrentIndex] = useState(0);

  // Navigation functions
  const handlePrev = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1
    );
  };

  const handleNext = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
    );
  };

  return (
    <section className="flex flex-col md:flex-row items-center justify-center gap-12 pt-2 pb-14 md:py-14 w-full" id="cases">
      <div className="flex md:gap-10 lg:gap-16 xl:gap-24 w-full flex-col lg:md:flex-row px-2 md:w-[1100px] md:flex-col items-center justify-center relative">
        <div className="w-full md:w-[416px] items-center md:items-start md:gap-8 flex flex-col">
          <div className="flex flex-col items-start relative self-stretch w-full">
            <div className="relative self-center md:self-stretch mt-[-1.00px] font-h3 font-[number:var(--h3-font-weight)] text-transparent text-[length:var(--h3-font-size)] tracking-[var(--h3-letter-spacing)] leading-[var(--h3-line-height)] [font-style:var(--h3-font-style)]">
              <span className="text-[#00dc7c] text-[80px] font-h3 [font-style:var(--h3-font-style)] font-[number:var(--h3-font-weight)] tracking-[var(--h3-letter-spacing)] leading-[var(--h3-line-height)] md:text-[length:var(--h3-font-size)]">
                +600
              </span>
              <span className="text-white font-h3 text-[80px] [font-style:var(--h3-font-style)] font-[number:var(--h3-font-weight)] tracking-[var(--h3-letter-spacing)] leading-[var(--h3-line-height)] md:text-[length:var(--h3-font-size)]">
                {" "}
                projetos
              </span>
            </div>

            <div className="w-full md:w-[485px] lg:w-[410px] xl:w-[485px] md:h-[200px] h-[200px] text-center relative left-[40px]">
              <img
                className="relative h-[140px] top-[20px] left-[-45px]"
                alt="Pincelada verde"
                src="/img/pincelada-verde.png"
              />
              <h2 className="relative self-stretch -mt-6 font-h3 font-normal pr-10 text-[#111111] text-[95px] md:text-[136px] lg:text-[110px] xl:text-[136px] tracking-[0] leading-[160px] ml-2 bottom-[145px]">
                Entregues
              </h2>
            </div>
          </div>

          <Button
            className="relative overflow-hidden px-10 py-9 bg-[#fc0081] transition duration-700 ease-in-out border-[5px] border-solid border-white font-buttons-and-input-24-b font-[number:var(--buttons-and-input-24-b-font-weight)] text-white text-[14px] md:text-[length:var(--buttons-and-input-24-b-font-size)] tracking-[var(--buttons-and-input-24-b-letter-spacing)] leading-[var(--buttons-and-input-24-b-line-height)] [font-style:var(--buttons-and-input-24-b-font-style)] mb-[-6.00px] hover:bg-[#d6006e] hover:scale-105 hover:shadow-lg group"
            onClick={(e) => {
              e.preventDefault();
              document.getElementById('contact')?.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
              });
            }}
            >
            <div className="absolute inset-0 bg-gradient-to-r from-[#49036F] to-[#75003c] translate-x-[-100%] group-hover:translate-x-[0%] transition-transform duration-700 ease-in-out"></div>
            <span className="relative z-10">VAMOS FAZER HISTÓRIAS JUNTOS</span>
          </Button>
        </div>

        <div className="flex flex-col w-full px-4 md:px-2 md:flex-row items-center gap-8 md:gap-14 relative">
          <div className="relative w-[178px] h-[160px] mt-10">
            <div className="absolute top-0 left-0 [font-family:'Microsoft_Tai_Le-Regular',Helvetica] font-normal text-transparent text-lg tracking-[0] leading-[27px]">
              <span className="text-white">Veja o que </span>
              <span className="text-[#00dc7c]">
                nossos <br />
                clientes dizem
              </span>
            </div>

            <img
              className="absolute w-[100px] md:w-[134px] md:h-14 top-[100px] left-[42px]"
              alt="Arrow"
              src="/img/arrow.png"
            />
          </div>

          <div className="flex flex-col items-start w-full">
            <div className="carousel-container w-full md:w-[442px] overflow-hidden">
              <div
                className="carousel-slider flex transition-transform duration-300 ease-in-out"
                style={{ transform: `translateX(-${currentIndex * 100}%)` }}
              >
                {testimonials.map((testimonial, index) => (
                  <Card
                    key={testimonial.id}
                    className="w-full md:w-[442px] flex-shrink-0 px-0 bg-[#23272d] rounded-none border-none"
                  >
                    <CardContent className="flex flex-col items-start justify-between gap-6 p-8 pb-4">
                      <div className="flex flex-col gap-6 text-left">
                        <div className="flex items-center gap-4 relative self-stretch w-full">
                          <div className="flex items-center gap-6 relative flex-1 grow">
                            <div className="flex flex-col items-start relative flex-1 grow">
                              <div className="relative self-stretch mt-[-1.00px] font-body-heavy font-[number:var(--body-heavy-font-weight)] text-white text-[length:var(--body-heavy-font-size)] tracking-[var(--body-heavy-letter-spacing)] leading-[var(--body-heavy-line-height)] [font-style:var(--body-heavy-font-style)]">
                                {testimonial.name}
                              </div>
                              <div className="relative self-stretch font-body-small-heavy font-[number:var(--body-small-heavy-font-weight)] text-[#00dc7c] text-[length:var(--body-small-heavy-font-size)] tracking-[var(--body-small-heavy-letter-spacing)] leading-[var(--body-small-heavy-line-height)] [font-style:var(--body-small-heavy-font-style)]">
                                {testimonial.company}
                              </div>
                            </div>
                          </div>
                          <img
                            className="w-20 h-20 rounded-[40px] object-cover border-[10px] bg-gradient-to-br from-[#FC0081] to-[#49036F] border-dashed border-[#23272d] relative self-stretch"
                            alt="Client profile"
                            src={testimonial.image}
                          />
                        </div>
                        <div className="relative self-stretch font-body-small font-[number:var(--body-small-font-weight)] text-[#bcbfc5] text-[length:20px] tracking-[var(--body-small-letter-spacing)] leading-[1.5] [font-style:var(--body-small-font-style)]">
                          {testimonial.text
                            .split("\n")
                            .map((paragraph, index) => (
                              <React.Fragment key={index}>
                                {paragraph}
                                {index <
                                  testimonial.text.split("\n").length - 1 && (
                                  <br />
                                )}
                              </React.Fragment>
                            ))}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
            
            {/* Controles estáticos fora do carrossel */}
            <div className="flex w-full md:w-[442px] items-center justify-between pt-4 px-8 bg-[#23272d] pb-6 relative -top-[1px]">
              <Button
                variant="outline"
                size="icon"
                className="w-[41px] h-[41px] bg-[#111111] text-[#00dc7c] rounded-[20.5px] border-none hover:bg-[#00dc7c] hover:text-[#111111] transition-colors duration-300"
                onClick={handlePrev}
              >
                <ChevronLeftIcon className="w-8 h-8" />
              </Button>
              <div className="flex gap-2">
                {testimonials.map((_, index) => (
                  <button
                    key={index}
                    className={`w-3 h-3 rounded-full transition-colors duration-300 ${
                      index === currentIndex
                        ? "bg-[#00dc7c] w-8"
                        : "bg-[#111111]"
                    }`}
                    onClick={() => setCurrentIndex(index)}
                  />
                ))}
              </div>
              <Button
                variant="outline"
                size="icon"
                className="w-[41px] h-[41px] bg-[#111111] text-[#00dc7c] rounded-[20.5px] border-none hover:bg-[#00dc7c] hover:text-[#111111] transition-colors duration-300"
                onClick={handleNext}
              >
                <ChevronRightIcon className="w-8 h-8" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
