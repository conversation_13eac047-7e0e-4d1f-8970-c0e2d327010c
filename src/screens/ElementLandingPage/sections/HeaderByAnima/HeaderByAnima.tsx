import React from "react";
import { Button } from "../../../../components/ui/button";
import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
} from "../../../../components/ui/navigation-menu";
import { MenuIcon, X } from "lucide-react";
import { useNavigate } from "react-router-dom";

export const HeaderByAnima = (): JSX.Element => {
  const navigate = useNavigate();
  const navItems = [
    { label: "Nossos Serviços", href: "#portfolio" },
    { label: "Cases", href: "#cases" },
    { label: "Sobre Nós", href: "#who-we-are" },
  ];

  const [isMobileMenuOpen, setIsMobileMenuOpen] = React.useState(false);
  const [isScrolled, setIsScrolled] = React.useState(false);

  React.useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  React.useEffect(() => {
    const scrollToAnchor = sessionStorage.getItem('scrollToAnchor');
    if (scrollToAnchor) {
      sessionStorage.removeItem('scrollToAnchor');
      setTimeout(() => {
        const target = document.querySelector(scrollToAnchor);
        if (target) {
          target.scrollIntoView({ behavior: 'smooth' });
        }
      }, 100);
    }
  }, []);

  const handleAnchorClick = (e: React.MouseEvent<HTMLAnchorElement>, href: string) => {
    e.preventDefault();
    const target = document.querySelector(href);
    if (target) {
      target.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className={`w-full z-50 fixed transition-all duration-300 ${
      isScrolled ? 'bg-[#111111]' : 'bg-transparent'
      }`}
    >
      <header className="flex top-0 scroll-smooth left-0 right-0 h-20 items-center justify-between py-6 px-4 mx-auto max-w-[1100px]">
        <a
          href="#"
          onClick={(e) => {
            e.preventDefault();
            if (window.location.pathname === '/talk-to-us') {
              window.location.href = `/`;
            } else {
              document.getElementById('#')?.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
              });
            }
          }}
        >
          <img
            className="w-[100.98px] h-[71.44px] object-cover"
            alt="Easymidia"
            src="img/logo.png"
          />
        </a>

        <NavigationMenu className="hidden md:block">
          <NavigationMenuList className="flex gap-2 px-[5px] py-1 rounded-md">
          {navItems.map((item, index) => (
            <NavigationMenuItem key={index}>
            <NavigationMenuLink
              href={item.href}
              onClick={(e) => {
                e.preventDefault();
                if (window.location.pathname === '/talk-to-us') {
                  window.location.href = `/`;
                  sessionStorage.setItem('scrollToAnchor', item.href);
                } else {
                  handleAnchorClick(e, item.href);
                }
              }}
              className="inline-flex items-start px-3 py-1.5 scroll-smooth"
            >
              <span className="[font-family:'Microsoft_Tai_Le-Bold',Helvetica] font-bold text-white text-xl tracking-[0] leading-6 whitespace-nowrap hover:text-pink-500 transition-colors duration-200">
              {item.label}
              </span>
            </NavigationMenuLink>
            </NavigationMenuItem>
          ))}
          </NavigationMenuList>
        </NavigationMenu>

        <div className="md:hidden relative">
          <Button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="p-2 bg-transparent border-none hover:bg-white/20"
            aria-label="Toggle menu"
          >
            <MenuIcon />
          </Button>

          <div className={`relative -right-[16px] transition-all duration-1000 ${isMobileMenuOpen ? '-top-[65px]' : '-top-[325px]'}`}>
            <div 
              className={`fixed inset-0 bg-black/50 z-40 transition-opacity duration-300 ${
              isMobileMenuOpen 
              ? 'opacity-100' 
              : 'opacity-0 pointer-events-none'
              }`}
              onClick={() => setIsMobileMenuOpen(false)}
            />
            
            <div 
              className={`absolute top-full right-0 mt-2 w-[100vw] bg-white shadow-lg z-50 transform transition-all duration-300 ease-out origin-top ${
              isMobileMenuOpen 
              ? 'opacity-100 scale-y-100 translate-y-0' 
              : 'opacity-0 scale-y-0 -translate-y-2 pointer-events-none'
              }`}
            >
              <div className="py-5 px-3 border-2 border-solid border-[#111111] scroll-smooth bg-[url('/img/bg_menu_mobile.png')] bg-cover">
                <div className="w-full text-right flex flex-row items-center justify-between mb-3">
                  <a href="/">
                    <img
                      className="w-[65.98px] h-[45.44px] object-cover"
                      alt="Easymidia"
                      src="img/logo.png"
                    />
                  </a>
                  <Button
                    onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                    className="p-2 bg-white/20 border-none hover:bg-white/20"
                    aria-label="Toggle menu"
                  >
                    <X className="w-16 h-16 text-white" />
                  </Button>
                </div>
                {navItems.map((item, index) => (
                  <a
                    key={index}
                    href={item.href}
                    onClick={(e) => {
                      e.preventDefault();
                      if (window.location.pathname === '/talk-to-us') {
                        window.location.href = `/`;
                        sessionStorage.setItem('scrollToAnchor', item.href);
                      } else {
                        handleAnchorClick(e, item.href);
                      }
                    }}
                    className="block px-4 py-3 text-md hover:bg-[#111111] hover:text-[#fc0081] transition-colors font-bold duration-200 text-[#ffffff]"
                  >
                    {item.label}
                  </a>
                ))}
                <div className="px-1 py-3">
                  <Button
                    className="w-full relative overflow-hidden px-5 py-6 bg-[#fc0081] transition duration-700 ease-in-out border-4 border-solid border-white font-buttons-and-input-24-b font-[number:var(--buttons-and-input-24-b-font-weight)] text-white text-sm tracking-[var(--buttons-and-input-24-b-letter-spacing)] leading-[var(--buttons-and-input-24-b-line-height)] [font-style:var(--buttons-and-input-24-b-font-style)] hover:bg-[#d6006e] hover:scale-105 hover:shadow-lg group"
                    onClick={() => navigate('/talk-to-us')}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-[#49036F] to-[#75003c] translate-x-[-100%] group-hover:translate-x-[0%] transition-transform duration-700 ease-in-out"></div>
                    <span className="relative z-10 text-lg">Fale conosco</span>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <Button
          className="relative hidden md:block overflow-hidden px-6 py-5 bg-[#fc0081] transition duration-700 ease-in-out border-[5px] border-solid border-white font-buttons-and-input-24-b font-[number:var(--buttons-and-input-24-b-font-weight)] text-white text-[length:var(--buttons-and-input-24-b-font-size)] tracking-[var(--buttons-and-input-24-b-letter-spacing)] leading-[var(--buttons-and-input-24-b-line-height)] [font-style:var(--buttons-and-input-24-b-font-style)] mb-[-6.00px] hover:bg-[#d6006e] hover:scale-105 hover:shadow-lg group"
          onClick={() => navigate('/talk-to-us')}
        >
          <div className="absolute inset-0 bg-gradient-to-r from-[#49036F] to-[#75003c] translate-x-[-100%] group-hover:translate-x-[0%] transition-transform duration-700 ease-in-out"></div>
          <span className="relative z-10 md:-top-[15px]">Fale conosco</span>
        </Button>
      </header>
    </div>
  );
}