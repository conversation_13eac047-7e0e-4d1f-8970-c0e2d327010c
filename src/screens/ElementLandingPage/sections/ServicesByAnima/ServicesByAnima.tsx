import React from "react";
import { Card, CardContent } from "../../../../components/ui/card";

export const ServicesByAnima = (): JSX.Element => {
  // Service data for mapping
  const services = [
    {
      id: 1,
      title: "Design que impacta",
      description:
        "Artes exclusivas para mídias sociais e impressos. Criatividade que destaca sua marca!",
      image: "/img/impactful_design.png",
    },
    {
      id: 2,
      title: "Gestão de Mídias",
      description:
        "Mantenha suas redes ativas! Planejamento e postagens estratégicas para engajar.",
      image: "/img/media_management.png",
    },
    {
      id: 3,
      title: "Marketing estratégico",
      description:
        "Transformamos ideias em impacto! Estratégias sob medida para seu crescimento.",
      image: "/img/strategic_marketing.png",
    },
    {
      id: 4,
      title: "Redação persuasiva",
      description:
        "Textos que vendem! Criamos conteúdos que atraem, engajam e convertem.",
      image: "/img/persuasive_writing.png",
    },
  ];

  return (
    <section className="relative w-full py-12 flex justify-center items-center overflow-hidden bg-[url('/img/bg_section_3.png')] bg-cover bg-no-repeat" id="services">
      {/* Services container */}
      <div className="relative flex flex-col md:grid md:grid-cols-2 lg:flex lg:flex-wrap lg:flex-row md:px-4 justify-between gap-8 w-[1100px] py-24">
        {services.map((service) => (
          <Card key={service.id} className="md:w-[100%] lg:w-[240px] bg-[#24272E] border-none">
            <CardContent className="flex flex-col items-center gap-4 p-0">
              <img
                className="w-48 h-40"
                alt={service.title}
                src={service.image}
              />

              <div className="flex flex-col items-start gap-2 w-full md:text-center lg:text-left">
                <h3 className="w-full mt-[-1.00px] font-[number:var(--body-heavy-font-weight)] font-black text-white text-[length:22px] tracking-[var(--body-small-heavy-letter-spacing)] leading-[var(--body-small-heavy-line-height)] [font-style:var(--body-small-heavy-font-style)]">
                  {service.title}
                </h3>

                <p className="w-full font-[number:var(--body-heavy-font-weight)] text-[#bcbfc5] text-[length:18px] tracking-[var(--body-small-letter-spacing)] leading-[1.8] [font-style:var(--body-small-font-style)]">
                  {service.description}
                </p>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </section>
  );
};
