import React from "react";

export const WhatWeAreByAnima = (): JSX.Element => {
  return (
    <section className="relative flex flex-col items-center justify-center w-full pb-28 pt-[300px] md:pt-[400px] lg:pt-[300px]">
      <div className="relative flex flex-col items-center justify-center w-[1100px]">
        <div className="relative flex flex-col items-center justify-center w-full">
          <div className="relative flex items-end self-stretch w-full">
            {/* Image container with gradient effects */}
            <div className="absolute w-[1021px] h-[754px] top-[-190px] left-[280px]">
              <div className="relative h-[754px]">
                <div className="absolute w-[650px] h-[400px] top-[175px] left-[-115px] bg-[#fc0081] rounded-[339.61px/219.58px] rotate-[-0.17deg] blur-[48px] opacity-[0.19]" />
                <div className="absolute w-[590px] h-[400px] top-[141px] left-[150px] bg-[#00dc7c] rounded-[294.75px/236.21px] rotate-[-0.17deg] blur-[100px] opacity-[0.19]" />
                <img
                  className="absolute w-[500px] h-[370px] object-contain md:w-[530px] md:h-[530px] lg:w-[620px] lg:h-[620px] md:-top-36 -top-20 lg:top-0 md:left-[160px] lg:left-[267px] md:object-cover z-[1]"
                  alt="Imagem do whatsapp"
                  src="img/img_section_2.png"
                />
              </div>
            </div>

            {/* Main heading */}
            <div className="flex flex-col gap-16 md:gap-0 md:flex-row items-center lg:flex-row w-full md:px-10 md:pt-20 lg:pt-0">
              <div className="relative w-full md:w-[600px] lg:w-[864px] mt-14 md:mt-[-1.00px] lg:mr-0 md:pl-[0px] lg:pl-[140px] xl:pl-0 z-[2]">
                <h2 className="flex flex-col leading-[65px] text-[length:70px] relative top-5 z-2 justify-center items-center text-white font-h1 font-[number:var(--h2-font-weight)] gap-4 tracking-[var(--h2-letter-spacing)] md:leading-[75px] lg:leading-[var(--h2-line-height)] md:text-[length:120px] lg:text-[length:154px]">
                  O que nós <br />
                  fazemos pela <br />
                  <div className="flex lg:relative lg:z-0 justify-center items-center md:leading-[0px] md:leading-[35px] text-[length:70px] lg:text-[length:70px] bg-[url('/img/pincelada-rosa.png')] bg-no-repeat bg-contain bg-center md:pb-[80px] md:pb-[120px] lg:pb-[140px] lg:pt-3 pl-[30px] pr-[10px]">
                    <span className="relative z-[2] -top-4 md:top-5 md:text-[length:120px] lg:text-[length:140px] lg:whitespace-nowrap">sua empresa?</span>
                  </div>
                </h2>
              </div>

              {/* Secondary text */}
              <div className="relative flex flex-col items-start w-full md:w-[382px] lg:w-[382px] z-[2] lg:mb-[25px] md:pt-10 lg:pt-56">
                <p className="relative self-stretch mt-[-1.00px] font-h3 whitespace-nowrap -top-[20px] text-white text-[52px] text-center tracking-[0] leading-[60px]">
                  Fazemos o público
                </p>
                <p className="relative self-stretch md:-mt-20 lg:-mt-6 text-center md:top-[25px] lg:top-[-25px]">
                  <span className="text-white text-[length:var(--h3-font-size)] leading-[0px] md:leading-[var(--h3-line-height)] font-h3 font-[number:var(--h3-font-weight)] tracking-[var(--h3-letter-spacing)]">
                    interagir
                  </span>
                </p>
                <div className="flex justify-center relative z-0 -top-3 md:top-0 items-center bg-[url('/img/pincelada-roxa_2.png')] w-full p-3 md:p-0 relative lg:top-[-35px] bg-center lg:w-[345px] lg:h-[90px] bg-no-repeat bg-contain">
                  <p className="relative self-stretch -top-2 md:mt-4 lg:mt-0 font-h3 text-white text-[48px] text-center md:top-[-20px] lg:top-[0px] tracking-[0] leading-[60px]">
                    e engajar com você!
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
