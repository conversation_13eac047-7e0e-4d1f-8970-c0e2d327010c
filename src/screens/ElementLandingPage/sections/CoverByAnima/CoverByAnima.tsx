import React from "react";
import { Button } from "../../../../components/ui/button";

export const CoverByAnima = (): JSX.Element => {
  return (
    <section className="flex flex-col items-center relative w-full xl:pt-14" id="#">
      <div className="relative w-full max-w-[1100px]">
        <div className="relative h-[843px] top-[70px] md:top-0 lg:top-0 xl:top-0">
          {/* Gradient overlays */}
          <div className="absolute w-[1098px] h-[424px] top-[304px] left-[72px] bg-[#fc0081] rounded-[549px/212px] blur-[74px] opacity-15" />
          <div className="absolute w-[455px] h-[259px] top-[230px] left-[780px] bg-[#fc0081] rounded-[227.5px/129.5px] blur-[92px] opacity-[0.19]" />
          <div className="absolute w-[455px] h-[425px] top-0 left-[345px] bg-[#a806ca] rounded-[227.5px/212.5px] blur-[92px] opacity-[0.19]" />

          {/* Main image */}
          <img
            className="absolute top-[140px] 2xl:gentle-sway-x md:w-[600px] md:h-[600px] lg:w-[739px] lg:h-[739px] top-[8px] md:left-[100px] lg:left-[175px] object-cover"
            alt="Img"
            src="img/img_section_1.png"
          />

          {/* Bottom shadow */}
          <div className="absolute w-[1232px] h-[357px] top-[586px] left-0 bg-black rounded-[616px/178.5px] blur-[100px]" />
        </div>
      </div>

      <div className="flex flex-col w-full max-w-[1088px] items-center gap-12 relative mt-[-435px]">
        <div className="flex flex-col items-center justify-center gap-6 relative self-stretch w-full">
          <div className="flex flex-col items-center relative self-stretch w-full">
            {/* Projects delivered text */}
            <div className="relative h-[65px] w-[380px] mt-[-1.00px] font-normal text-white text-[40px] text-center tracking-[0] leading-[48px] [font-family:'Thunderhouse_Pro'] bg-[url('/img/pincelada-roxa.png')] bg-cover bg-center bg-no-repeat">
              + 600 projetos entregues
            </div>

            {/* Main heading with colored text */}
            <div className="relative flex flex-col self-stretch font-h2 font-[number:var(--h2-font-weight)] text-transparent text-[length:var(--h2-font-size)] text-center tracking-[var(--h2-letter-spacing)] leading-[70px] [font-style:var(--h2-font-style)]">
              <span className="text-white leading-[60px] text-[65px] lg:h-[60px] font-h2 [font-style:var(--h2-font-style)] font-[number:var(--h2-font-weight)] tracking-[var(--h2-letter-spacing)] md:leading-[100px] lg:leading-[70px] md:text-[length:var(--h2-font-size)]">
                Transformamos eventos em{" "}
              </span>
              <span className="text-[#00dc7c] h-[200px] text-[90px] md:pt-5 leading-[70px] font-h2 [font-style:var(--h2-font-style)] font-[number:var(--h2-font-weight)] tracking-[var(--h2-letter-spacing)] md:leading-[100px] lg:leading-[180px] md:text-[length:var(--h2-font-size)]">
                experiências inesquecíveis!
              </span>
            </div>
          </div>

          {/* Description text */}
          <p className="relative w-full max-w-[916px] font-body-small px-4 md:pt-14 lg:pt-4 font-[number:var(--body-small-font-weight)] text-white text-[length:var(--body-small-font-size)] text-center tracking-[var(--body-small-letter-spacing)] leading-[var(--body-small-line-height)] [font-style:var(--body-small-font-style)]">
            A EasyMídia conecta marcas ao público de forma humanizada, criativa
            e estratégica, transformando cada interação em uma experiência
            autêntica e memorável.
          </p>
        </div>

        {/* Call to action buttons */}
        <div className="flex items-center flex-col lg:flex-row md:flex-row justify-center gap-8 relative scroll-smooth">
          <Button
            className="relative overflow-hidden px-10 py-9 bg-[#fc0081] transition duration-700 ease-in-out border-[5px] border-solid border-white font-buttons-and-input-24-b font-[number:var(--buttons-and-input-24-b-font-weight)] text-white text-[length:var(--buttons-and-input-24-b-font-size)] tracking-[var(--buttons-and-input-24-b-letter-spacing)] leading-[var(--buttons-and-input-24-b-line-height)] [font-style:var(--buttons-and-input-24-b-font-style)] mb-[-6.00px] hover:bg-[#d6006e] hover:scale-105 hover:shadow-lg group"
            onClick={(e) => {
              e.preventDefault();
              document.getElementById('contact')?.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
              });
            }}
            >
            <div className="absolute inset-0 bg-gradient-to-r from-[#49036F] to-[#75003c] translate-x-[-100%] group-hover:translate-x-[0%] transition-transform duration-700 ease-in-out"></div>
            <span className="relative z-10">QUERO UM EVENTO ÉPICO</span>
          </Button>

            <a
              href="#portfolio"
              className="px-8 py-4 font-buttons-and-input-24-b font-[number:var(--buttons-and-input-24-b-font-weight)] text-[#00dc7c] text-[length:var(--buttons-and-input-24-b-font-size)] tracking-[var(--buttons-and-input-24-b-letter-spacing)] leading-[var(--buttons-and-input-24-b-line-height)] [font-style:var(--buttons-and-input-24-b-font-style)]"
              onClick={(e) => {
                e.preventDefault();
                document.getElementById('portfolio')?.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
                });
              }}
            >
            VER PORTFÓLIO
            </a>
        </div>
      </div>
    </section>
  );
};
