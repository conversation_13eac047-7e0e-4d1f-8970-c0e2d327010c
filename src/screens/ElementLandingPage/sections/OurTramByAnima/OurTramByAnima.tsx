import React from "react";
import { Card, CardContent } from "../../../../components/ui/card";

export const OurTramByAnima = (): JSX.Element => {
  // Team members data for mapping
  const teamMembers = [
    {
      name: "<PERSON>",
      description:
        "Design é com ele! Cria visuais únicos que encantam e comunicam perfeitamente.",
      image: "/img/people_1.png",
    },
    {
      name: "<PERSON><PERSON>",
      description:
        "O mestre da gestão de tráfego! Faz os anúncios performarem como mágica.",
      image: "/img/people_2.png",
    },
    {
      name: "<PERSON>",
      description:
        "Lara é conteúdo puro! Escreve, cria e conecta marcas com autenticidade e estratégia.",
      image: "/img/people_3.png",
    },
    {
      name: "<PERSON>",
      description:
        "Transforma ideias em cenas! Roteiriza, edita e dá ritmo às histórias certas.",
      image: "/img/people_4.png",
    },
    {
      name: "<PERSON>",
      description: "<PERSON>ssa mascote! A gata que manda em tudo com muito estilo.",
      image: "/img/people_5.png",
    },
  ];

  return (
    <section className="flex flex-col md:flex-row items-center justify-center gap-8 px-6 md:px-28 py-14 relative w-full max-w-full mx-auto">
      <div className="flex md:w-[1100px] lg:gap-5 flex-col md:flex-row">
        <div className="flex flex-col items-start gap-24 md:gap-36 z-[2] w-full md:w-1/2">
          <div className="relative">
            <h2 className="relative w-full font-h3 text-white top-2 text-[110px] md:text-[clamp(120px,15vw,304px)] leading-[0.8em] mt-[-1px] z-[2]">
              Nosso Time
            </h2>
            <img
              className="absolute md:right-[180px] -left-[100px] bottom-[-55px] z-[1] transform translate-x-[20%] translate-y-[10%]"
              alt="Pincelada decorativa"
              src="/img/pincelada-roxa_3.png"
            />
          </div>

          <p className="relative w-full pr-10 font-body-small text-[#bcbfc5] text-[length:21px] pb-10 tracking-[var(--body-small-letter-spacing)] leading-[1.5]">
            Por trás de grandes ideias, existe um time incrível!&nbsp;&nbsp;
            <br />
            Na EasyMídia, reunimos mentes criativas, estratégicas e apaixonadas
            por transformar marcas em experiências únicas.
          </p>
        </div>

        {/* Right column - Team members */}
        <div className="flex flex-col items-start gap-8 z-[1] w-full md:w-1/2">
          {teamMembers.map((member, index) => (
            <Card
              key={index}
              className="w-full bg-transparent border-0 shadow-none px-0 md:px-4"
            >
              <CardContent className="flex items-center flex-row-reverse md:flex-row justify-center gap-8 p-0">
                <div className="flex-col text-left md:text-center lg:text-left items-center justify-end gap-2 flex-1 grow flex">
                  <h3 className="relative self-stretch mt-[-1px] text-[#fc0081] text-[length:var(--body-heavy-font-size)] font-[number:var(--body-heavy-font-weight)] tracking-[var(--body-heavy-letter-spacing)] leading-[var(--body-heavy-line-height)]">
                    {member.name}
                  </h3>
                  <p className="relative self-stretch font-body-small text-[#bcbfc5] text-[length:21px] tracking-[var(--body-small-letter-spacing)] leading-[1.5]">
                    {member.description}
                  </p>
                </div>
                <div className="h-full">
                  <img
                    className="w-[100px] h-[100px] rounded-[55px] object-cover border-[10px] bg-gradient-to-br from-[#FC0081] to-[#49036F] border-dashed border-[#111111] relative self-stretch"
                    alt={`Foto de ${member.name}`}
                    src={member.image}
                  />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};
