import React from "react";
import { Card, CardContent } from "../../../../components/ui/card";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";

export const PortfolioByAnima = (): JSX.Element => {
  // Client/partner logos data
  const clientLogos = [
    {
      src: "/img/arapuca.png",
      alt: "Imagem Arapuca",
      width: "108px",
      height: "112px",
    },
    {
      src: "/img/domingao_no_grau.png",
      alt: "Imagem Domingão no Grau",
      width: "195px",
      height: "89px",
    },
    {
      src: "/img/ultrimagem.png",
      alt: "Imagem Ultrimagem",
      width: "159px",
      height: "96px",
      objectCover: true,
    },
    {
      src: "/img/zine.png",
      alt: "Imagem Zine Cultural",
      width: "223px",
      height: "89px",
      objectCover: true,
    },
    {
      src: "/img/itatiaia.png",
      alt: "Imagem Itatiaia",
      width: "149px",
      height: "111px",
      objectCover: true,
    },
    {
      src: "/img/maresias.png",
      alt: "Imagem Maresias",
      width: "148px",
      height: "115px",
    },
  ];

  return (
    <section className="flex flex-col items-center gap-[108px] py-28 relative w-full bg-[url('/img/bg_section_4.png')] bg-cover bg-center" id="portfolio">
      {/* Heading section with brush stroke */}
      <div className="flex flex-col items-start relative w-full max-w-[868px]">
        <h1 className="relative w-full mt-[-1.00px] font-h1 font-[number:var(--h1-font-weight)] text-transparent text-[length:var(--h1-font-size)] tracking-[var(--h1-letter-spacing)] leading-[var(--h1-line-height)] [font-style:var(--h1-font-style)]">
          <span className="w-full lg:w-[1000px] md:w-[1000px] lg:justify-start text-[85px] items-center flex relative left-5 text-white font-h1 md:justify-center [font-style:var(--h1-font-style)] font-[number:var(--h1-font-weight)] tracking-[var(--h1-letter-spacing)] leading-[var(--h1-line-height)] md:text-[length:var(--h2-font-size)] lg:text-[length:var(--h1-font-size)]">
            Nosso{" "}
            <div className="flex justify-center items-center bg-[url('/img/pincelada-rosa_3.png')] xl:top-12 md:top-10 lg:top-4 md:bg-contain lg:bg-auto h-[85px] bg-no-repeat relative md:top-[75px] lg:top-[50px] w-full md:w-[490px] md:h-[110px] lg:w-[590px] lg:h-[210px]">
              <span className="relative md:bottom-[40px] md:-left-[80px] lg:-top-12 lg:bottom-[65px] lg:left-[10px]">
                portifa
              </span>
            </div>
          </span>
        </h1>
      </div>

      {/* Portfolio showcase card */}
      <Card className="w-full max-w-[1088px] h-[528px] bg-[#23272d] rounded-3xl border-none md:px-4">
        <CardContent className="flex items-center justify-center p-10 h-full">
          <h2 className="font-h2 font-[number:var(--h2-font-weight)] text-white md:h-[305px] text-[75px] md:text-[length:var(--h2-font-size)] text-center tracking-[var(--h2-letter-spacing)] leading-[60px] md:leading-[var(--h2-line-height)] [font-style:var(--h2-font-style)]">
            Motion com as imagens dos projetos
          </h2>
        </CardContent>
      </Card>

      {/* Client logos section */}
      <div className="w-full pt-[50px] sm:pt-[100px] md:pt-[190px] xl:py-5 md:py-10">
        <div className="w-full md:max-w-[1088px] mx-auto">
          <Slider
            dots={false}
            arrows={false}
            infinite={true}
            speed={4000}
            slidesToShow={4}
            slidesToScroll={1}
            autoplay={true}
            autoplaySpeed={10}
            pauseOnHover={false}
            cssEase="linear"
            responsive={[
              {
                breakpoint: 1024,
                settings: {
                  slidesToShow: 3,
                },
              },
              {
                breakpoint: 768,
                settings: {
                  slidesToShow: 2,
                },
              },
              {
                breakpoint: 480,
                settings: {
                  slidesToShow: 1,
                },
              },
            ]}
          >
            {clientLogos.map((logo, index) => (
              <div
                key={index}
                className="px-4 flex justify-center items-center h-[120px]"
              >
                <img
                  className={`${
                    logo.objectCover ? "object-cover" : "object-contain"
                  } mx-auto`}
                  alt={logo.alt}
                  src={logo.src}
                  style={{ width: logo.width, height: logo.height }}
                />
              </div>
            ))}
          </Slider>
        </div>
      </div>
    </section>
  );
};
