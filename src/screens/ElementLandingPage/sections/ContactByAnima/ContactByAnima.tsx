import React from "react";
import { Button } from "../../../../components/ui/button";
import { useNavigate } from "react-router-dom";

export const ContactByAnima = (): JSX.Element => {
  const navigate = useNavigate();
  return (
    <section className="flex items-center py-14 relative bg-[url('/img/bg_section_7.png')] justify-center bg-cover bg-center w-full" id="contact">
      <div className="flex w-[1100px] flex-col md:flex-row">
        <div className="relative w-full md:max-w-[829px] top-[180px] z-3">
          <div className="relative">
            {/* Pink glow effect */}
            <div className="absolute w-[440px] h-[305px] top-0 left-[38px] bg-[#fc0081] rounded-[385px/394px] blur-[92px] opacity-[0.19]" />

            {/* Shadow elements */}
            <div className="absolute hidden w-[250px] h-[30px] md:hidden top-[690px] left-[55px] bg-[#111111] rounded-[186px/17.5px]" />
            <div className="absolute hidden w-[250px] h-[30px] md:hidden top-[685px] left-[315px] bg-[#111111] rounded-[186px/17.5px]" />

            {/* Main image */}
            <img
              className="relative w-[470px] sm:w-[440px] h-[415px] -top-[120px] md:top-0 md:h-[305px] md:w-[827px] md:h-[827px] lg:w-[827px] lg:h-[827px] lg:-top-32 left-0 object-contain sm:object-cover md:object-contain z-[2]"
              alt="Person for contact section"
              src="/img/img_section_7.png"
            />
          </div>
        </div>

        <div className="flex flex-col w-full max-w-[640px] md:top-[60px] items-center md:gap-24 lg:gap-36 xl:gap-0 relative lg-ml-10">
          <div className="flex flex-col items-center relative self-stretch w-full">
            {/* First line of text */}
            <div className="inline-flex items-center gap-4 md:-left-[5px] relative left-[40px] xl:left-[0px]">
              <div className="md:w-[208px] md:h-[296px] lg:w-[238px] lg:h-[296px] mt-[-1.00px] top-[10px] text-[#00dc7c] text-[170px] md:text-[200px] lg:text-[244px] left-[20px] leading-[240px] relative font-h3 font-normal tracking-[0]">
                Seu
              </div>

              <div className="flex flex-col w-[324px] items-start relative md:top-[0px] lg:top-[10px] right-[30px]">
                <div className="self-stretch mt-[-1.00px] text-[#fc0081] top-[65px] md:top-[20px] left-[50px] xl:left-[10px] xl:top-[10px] md:text-[length:90px] lg:top-[10px] lg:left-[0] lg:text-[length:130px] xl:text-[length:110px] text-[80px] 2xl:leading-[var(--h2-line-height)] relative font-h2 font-[number:var(--h2-font-weight)] tracking-[var(--h2-letter-spacing)] [font-style:var(--h2-font-style)]">
                  Evento
                </div>

                <div className="self-stretch text-white -left-[15px] sm:-left-[15px] top-[8px] sm:top-[9px] md:-mt-[45px] md:-left-[50px] lg:-mt-[28px] left-[-10px] xl:left-[-25px] xl:top-[10px] xl:text-[100px] lg:-top-[30px] lg:-left-[22px] md:text-[length:90px] text-[75px] lg:text-[length:110px] text-center leading-[var(--h2-line-height)] relative font-h2 font-[number:var(--h2-font-weight)] tracking-[var(--h2-letter-spacing)] [font-style:var(--h2-font-style)]">
                  precisa
                </div>
              </div>
            </div>

            {/* Second line of text */}
            <div className="inline-flex items-center gap-2 md:gap-3 lg:gap-6 relative md:left-[-30px] -top-10 md:top-[-80px] lg:top-[-40px]">
              <div className="relative w-fit mt-[-1.00px] font-h3 font-normal text-[100px] text-white md:text-[120px] lg:text-[140px] text-center tracking-[0] leading-[120px] whitespace-nowrap">
                de
              </div>

              <div className="relative w-fit mt-[-1.00px] font-h3 font-normal text-[100px] text-white md:text-[120px] lg:text-[140px] text-center tracking-[0] leading-[120px] whitespace-nowrap">
                um
              </div>

              <div className="w-fit mt-[-1.00px] text-[#fc0081] md:text-[120px] text-[100px] lg:text-[140px] text-center leading-[120px] whitespace-nowrap relative font-h3 font-normal tracking-[0]">
                design
              </div>
            </div>

            {/* Third line of text */}
            <div className="w-fit text-[#fc0081] text-[210px] -top-28 md:text-[250px] lg:text-[310px] lg:-top-3 xl:-top-28 leading-[240px] whitespace-nowrap md:left-[-30px] relative md:-mt-[145px] lg:-mt-[65px] xl:mt-[35px] font-h3 font-normal tracking-[0]">
              f*da!
            </div>
          </div>

          {/* CTA Button */}
          <Button
            className="relative overflow-hidden px-10 py-9 bg-[#fc0081] transition duration-700 ease-in-out border-[5px] border-solid border-white font-buttons-and-input-24-b font-[number:var(--buttons-and-input-24-b-font-weight)] text-white text-[length:var(--buttons-and-input-24-b-font-size)] tracking-[var(--buttons-and-input-24-b-letter-spacing)] leading-[var(--buttons-and-input-24-b-line-height)] [font-style:var(--buttons-and-input-24-b-font-style)] mb-[-6.00px] hover:bg-[#d6006e] hover:scale-105 hover:shadow-lg group"
            onClick={() => navigate('/talk-to-us')}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-[#49036F] to-[#75003c] translate-x-[-100%] group-hover:translate-x-[0%] transition-transform duration-700 ease-in-out"></div>
            <span className="relative z-10">ENTRAR EM CONTATO</span>
          </Button>
        </div>
      </div>
    </section>
  );
};
