import React from "react";

export const WhoWeAreByAnima = (): JSX.Element => {
  return (
    <section className="flex flex-col md:flex-row items-center justify-center py-14 w-full" id="who-we-are">
      <div className="flex flex-col md:flex-row md:w-[1100px]">
        <div className="relative w-full md:w-1/2 aspect-square">
          <div className="relative h-full">
            <div className="absolute w-[73%] h-[79%] top-[11%] left-[18%] bg-[#fc0081] rounded-[286.5px/309.5px] blur-[92px] opacity-[0.19]" />
            <img
              className="absolute lg:gentle-sway-y w-full h-full top-0 left-0 object-contain z-[2]"
              alt="Company image"
              src="/img/img_section_6.png"
            />
          </div>
        </div>
        <div className="flex flex-col w-full md:w-1/2 items-start relative">
          <img
            className="absolute md:w-[218%] h-[200px] bg-contain top-[50px] left-2 md:top-[49%] md:left-[-12%] -translate-y-1/2"
            alt="Pink brush stroke"
            src="/img/pincelada-rosa_4.png"
          />

          <div className="w-full flex flex-col gap-24 xl:gap-16 relative z-10 md:top-[200px] sm:left-0 sm:-top-10 md:-left-14">
            <h2 className="relative mt-[-1.00px] font-h3 text-white pl-5 text-[6rem] sm:text-[10rem] md:text-[8rem] lg:text-[8.5rem] tracking-[0] leading-[0.8] z-[1]">
              Quem Somos?
            </h2>

            <p className="font-body-small font-[number:var(--body-small-font-weight)] px-3 pt-10 md:pt-6 lg:pb-8 text-[#bcbfc5] text-[length:var(--body-small-font-size)] tracking-[var(--body-small-letter-spacing)] leading-[var(--body-small-line-height)] [font-style:var(--body-small-font-style)]">
              Somos a EasyMídia! Conectamos marcas ao público de forma
              humanizada, criativa e estratégica. Nosso propósito é impulsionar
              negócios com campanhas inovadoras e envolventes, sempre prezando
              pela ética, criatividade e excelência.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};
