import {
  Ri<PERSON>hatsappFill,
  Ri<PERSON>nstagram<PERSON>ill,
  RiTwitterXFill,
} from "react-icons/ri";
import { FaFacebook } from "react-icons/fa";
import { ContactByAnima } from "./sections/ContactByAnima";
import { CoverByAnima } from "./sections/CoverByAnima";
import { HeaderByAnima } from "./sections/HeaderByAnima";
import { OurTramByAnima } from "./sections/OurTramByAnima";
import { PortfolioByAnima } from "./sections/PortfolioByAnima";
import { ProjectsByAnima } from "./sections/ProjectsByAnima/ProjectsByAnima";
import { ServicesByAnima } from "./sections/ServicesByAnima";
import { WhatWeAreByAnima } from "./sections/WhatWeAreByAnima";
import { WhoWeAreByAnima } from "./sections/WhoWeAreByAnima";
import { useEffect, useState } from "react";

export const ElementLandingPage = (): JSX.Element => {
  const [fontLoaded, setFontLoaded] = useState(false);

  useEffect(() => {
    const font = new FontFace('SuaFonte', 'url(https://db.onlinewebfonts.com/t/e8fafd4bab1ff229bb62067775369b8d.woff)');
    
    font.load().then(() => {
      document.fonts.add(font);
      setFontLoaded(true);
    }).catch(() => {

      setTimeout(() => setFontLoaded(true), 2000);
    });
  }, []);

  if (!fontLoaded) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="flex flex-col w-full justify-center items-center min-h-screen bg-[#111111] overflow-hidden">
      <main className="flex flex-col w-full items-center">
        <div className="flex flex-col items-center bg-[url('/img/bg_section_1.png')] bg-cover bg-bottom bg-no-repeat w-full h-full relative">
          <HeaderByAnima />
          <CoverByAnima />
        </div>
        <WhatWeAreByAnima />
        <ServicesByAnima />
        <PortfolioByAnima />
        <ProjectsByAnima />
        <WhoWeAreByAnima />
        <OurTramByAnima />
        <ContactByAnima />
      </main>

      <footer className="w-full sm:w-full md:w-[100%] lg:w-[1100px] h-[100%] md:h-[115px] py-4 bg-[#111111]">
        <div className="flex flex-col items-center w-full sm:w-full md:w-full sm:flex-col md:flex-col lg:flex-row justify-between">
          <div className="flex items-center gap-2 w-full md:w-auto lg:w-auto justify-between px-4">
            <img
              className="w-[115px] h-[82px] object-cover"
              alt="Easymidia"
              src="/img/logo.png"
            />

            <div className="flex items-center gap-2">
              <a href="">
                <RiWhatsappFill className="w-8 h-8 text-[#00DC7C]" />
              </a>

              <a href="">
                <FaFacebook className="w-7 h-7 text-[#00DC7C]" />
              </a>
              <a href="https://www.instagram.com/agenciaeasymidia/">
                <RiInstagramFill className="w-8 h-8 text-[#00DC7C]" />
              </a>
              <a href="">
                <RiTwitterXFill className="w-8 h-8 text-[#00DC7C]" />
              </a>
            </div>
          </div>

          <div className="font-body-small font-[number:var(--body-small-font-weight)] text-white text-[14px] sm:text-[14px] md:text-[length:var(--body-small-font-size)] text-center tracking-[var(--body-small-letter-spacing)] leading-[var(--body-small-line-height)] md:whitespace-nowrap whitespace-wrap px-4 [font-style:var(--body-small-font-style)]">
            © 2025 Agência Easy Mídia. Todos os direitos reservados.
          </div>
        </div>
      </footer>
    </div>
  );
};
