# EasyMídia

Landing Page para agência de marketing e design EasyMídia.

## 🚀 Começando

### Pré-requisitos

- Typescript
- yarn

### Instalação

1. Clone o repositório:
```bash
git clone <https://github.com/htgabriel/easymidia.git>
```

2. Instale as dependências:
```bash
yarn
```

### Desenvolvimento

Execute o projeto em modo de desenvolvimento:
```bash
yarn dev
```

O projeto estará disponível em [http://localhost:5173/](http://localhost:5173/)

## 📁 Estrutura do Projeto

```
easymidia/
├── src/
├── public/
├── package.json
└── README.md
```

## 🛠️ Tecnologias

- Vite
- JavaScript/TypeScript
- HTML5
- CSS3
